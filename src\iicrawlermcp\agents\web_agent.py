"""
Web Agent module for iICrawlerMCP.

This module provides a unified agent that combines DOM analysis and browser automation.
The WebAgent merges the capabilities of BrowserAgent and ElementAgent to provide
seamless web automation without the complexity of inter-agent communication.

The WebAgent can analyze DOM structures and execute browser operations in a single,
coherent workflow, eliminating the element selector guessing problem.
"""

import logging
from typing import Optional, List
from .prompts import get_web_agent_prompt
from langchain.agents import create_openai_functions_agent, AgentExecutor
from langchain_openai import Chat<PERSON><PERSON><PERSON><PERSON>
from langchain_core.tools import BaseTool

from ..core.config import config


logger = logging.getLogger(__name__)


class WebAgent:
    """
    A unified agent for complete web automation tasks.

    This agent combines DOM analysis and browser automation capabilities:
    - DOM analysis: Precise element discovery and content extraction
    - Browser automation: Navigation, clicking, typing, scrolling, screenshots
    - Unified workflow: Analyze DOM → Get precise selectors → Execute operations
    - No inter-agent communication overhead

    All web automation tasks are performed through the invoke() method using natural
    language instructions, with automatic DOM analysis and browser operation execution.
    """

    def __init__(
        self,
        tools: Optional[List[BaseTool]] = None,
        verbose: Optional[bool] = None,
        llm_config: Optional[dict] = None,
    ):
        """
        Initialize the WebAgent.

        Args:
            tools: List of LangChain tools to use. If None, uses web automation tools.
            verbose: Whether to enable verbose logging. If None, uses config default.
            llm_config: Custom LLM configuration. If None, uses config defaults.
        """
        self.tools = tools or self._get_web_tools()
        self.verbose = verbose if verbose is not None else config.VERBOSE
        self.llm_config = llm_config or config.get_llm_config()

        self._llm = None
        self._agent = None
        self._executor = None

    def _get_web_tools(self) -> List[BaseTool]:
        """
        Get unified web automation tools combining DOM analysis, browser operations, and search.

        Returns 15 core tools optimized for booking.com-style complex websites:
        - 9 browser operation tools: navigate, click, type, select, scroll, element scroll, screenshot, page info, close
        - 4 DOM analysis tools: inputs, content, smart interaction (includes buttons), links
        - 2 search tools: web search for domain lookup and web fetch for content analysis
        """
        from ..tools.browser_tools import (
            navigate_browser,
            click_element,
            type_text,
            select_option,
            scroll_page,
            scroll_to_element,
            take_screenshot,
            get_page_info,
            close_browser,
        )
        from ..tools.dom_tools import (
            dom_get_inputs_enhanced,
            dom_get_content_elements,
            dom_get_interactive_elements_smart,
        )
        from ..tools.search_tools import get_search_tools

        # 9 essential browser operation tools
        browser_tools = [
            navigate_browser,  # Page navigation
            click_element,  # Click interactions (requires XPath from DOM tools)
            type_text,  # Text input (requires XPath from DOM tools)
            select_option,  # Dropdown selection (requires XPath from DOM tools)
            scroll_page,  # Page scrolling for long content
            scroll_to_element,  # Scroll to specific elements
            take_screenshot,  # State recording and debugging
            get_page_info,  # Page information (title, URL, viewport)
            close_browser,  # Browser cleanup and resource management
        ]

        # 3 essential DOM analysis tools
        dom_tools = [
            dom_get_inputs_enhanced,  # Find all input elements (text, select, textarea)
            dom_get_content_elements,  # Extract text content (prices, reviews, etc.)
            dom_get_interactive_elements_smart,  # Smart discovery for complex components (includes buttons)
        ]

        # 2 search tools for domain lookup, technical research, and content analysis
        search_tools = get_search_tools()

        web_tools = browser_tools + dom_tools + search_tools
        logger.info(
            f"WebAgent configured with {len(web_tools)} unified tools: {len(browser_tools)} browser + {len(dom_tools)} DOM + {len(search_tools)} search"
        )

        return web_tools

    def _create_llm(self) -> ChatOpenAI:
        """Create and configure the LLM instance."""
        if self._llm is None:
            try:
                self._llm = ChatOpenAI(**self.llm_config)
                logger.info(
                    f"WebAgent LLM created with model: {self.llm_config.get('model')}"
                )
            except Exception as e:
                logger.error(f"Failed to create WebAgent LLM: {e}")
                raise
        return self._llm

    def _create_agent(self) -> None:
        """Create the LangChain agent with web automation specific prompt."""
        if self._agent is None:
            try:
                llm = self._create_llm()
                # Use web automation specific prompt
                prompt = get_web_agent_prompt()
                self._agent = create_openai_functions_agent(llm, self.tools, prompt)
                logger.info("WebAgent created successfully")
            except Exception as e:
                logger.error(f"Failed to create WebAgent: {e}")
                raise

    def _create_executor(self) -> AgentExecutor:
        """Create the agent executor."""
        if self._executor is None:
            self._create_agent()
            try:
                self._executor = AgentExecutor(
                    agent=self._agent,
                    tools=self.tools,
                    verbose=self.verbose,
                    max_iterations=50,  # Support complex multi-step tasks
                    handle_parsing_errors=True,
                    return_intermediate_steps=True,  # 返回中间步骤，用于代码生成
                )
                logger.info("WebAgent executor created successfully")
            except Exception as e:
                logger.error(f"Failed to create WebAgent executor: {e}")
                raise
        return self._executor

    def invoke(self, input_text: str) -> dict:
        """
        Execute a complete web automation task using the agent.

        This method handles the full workflow:
        1. Analyze the task requirements
        2. Use DOM tools to discover elements and get precise selectors
        3. Execute browser operations using the discovered selectors
        4. Extract results and verify completion

        Args:
            input_text: The task description or instruction for the web agent.

        Returns:
            A dictionary containing the agent's response and output.
        """
        executor = self._create_executor()

        try:
            logger.info(f"WebAgent executing task: {input_text}")
            result = executor.invoke({"input": input_text})
            logger.info("WebAgent task completed successfully")
            return result
        except Exception as e:
            logger.error(f"WebAgent task execution failed: {e}")
            raise

    def cleanup(self) -> None:
        """Clean up resources used by the web agent."""
        try:
            # Clear agent references
            self._llm = None
            self._agent = None
            self._executor = None
            logger.info("WebAgent cleanup completed")
        except Exception as e:
            logger.error(f"Error during WebAgent cleanup: {e}")


def build_web_agent(
    tools: Optional[List[BaseTool]] = None,
    verbose: Optional[bool] = None,
    llm_config: Optional[dict] = None,
) -> WebAgent:
    """
    Build and return a configured WebAgent instance.

    Args:
        tools: List of LangChain tools to use. If None, uses web automation tools.
        verbose: Whether to enable verbose logging. If None, uses config default.
        llm_config: Custom LLM configuration. If None, uses config defaults.

    Returns:
        WebAgent: A fully configured web automation agent.
    """
    try:
        agent = WebAgent(tools=tools, verbose=verbose, llm_config=llm_config)
        logger.info("WebAgent built successfully")
        return agent
    except Exception as e:
        logger.error(f"Failed to build WebAgent: {e}")
        raise
