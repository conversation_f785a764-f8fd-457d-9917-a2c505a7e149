"""
Unified Browser Tools for iICrawlerMCP.

This module provides a comprehensive set of browser automation tools organized
in a toolkit pattern following industry best practices from LangChain, CrewAI,
and Playwright frameworks.

The tools are organized in two tiers:
- Basic Tools: Core operations for specialized agents (7 tools)
- Advanced Tools: Feature-rich operations for general-purpose agents (5 tools)

Optimization completed: Removed duplicate tools, added scroll tools, standardized APIs.
"""

import logging
from typing import Optional, List
from langchain_core.tools import tool, BaseTool
from ..core.browser import get_global_browser
from ..core.browser_thread import run_browser_call

logger = logging.getLogger(__name__)

# -----------------------------------------------------------------------------
# internal helper: safely run blocking Playwright sync calls even inside an
# active asyncio event loop, without changing the public sync signature of the
# browser tools. If we're already in an event loop, we off-load to a worker
# thread via anyio.to_thread.run_sync; otherwise we execute directly.
# -----------------------------------------------------------------------------

# run_browser_call executes callable in dedicated browser worker thread


# =============================================================================
# BASIC BROWSER TOOLS - Simple, reliable operations for specialized agents
# =============================================================================


@tool
def navigate_browser(url: str, wait_until: str = "domcontentloaded") -> str:
    """
    Navigate the browser to the specified URL with wait options.

    Args:
        url: The URL to navigate to (e.g., "https://google.com")
        wait_until: When to consider operation complete:
                   - "load": Wait for load event
                   - "domcontentloaded(default)": Wait for DOM content loaded
                   - "networkidle": Wait for network idle

    Returns:
        A success message indicating navigation completion.

    Example:
        navigate_browser("https://google.com")
        navigate_browser("https://spa-app.com", wait_until="domcontentloaded")
    """
    try:
        browser = get_global_browser()
        run_browser_call(lambda: browser.navigate(url, wait_until=wait_until))
        success_msg = f"🌐 Successfully navigated to: {url} (wait_until={wait_until})"
        logger.info(f"BrowserTool navigate: {success_msg}")
        return success_msg
    except Exception as e:
        error_msg = f"Navigation failed: {str(e)}"
        logger.error(f"BrowserTool navigate error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def take_screenshot(
    filename: Optional[str] = None,
    full_page: bool = False,
    element_selector: Optional[str] = None,
) -> str:
    """
    Take a screenshot of the current page with advanced options.

    Screenshots are automatically saved to the project's screenshots directory.
    If no filename is provided, an auto-generated timestamp-based name is used.

    Args:
        filename: File name to save the screenshot to (without path).
                 Auto-generated timestamp name if not specified.
        full_page: When true, takes a screenshot of the full scrollable page.
                  When false, captures only the visible viewport.
                  Cannot be used with element_selector.
                  Default is False. Only set to True when explicitly requested by user
        element_selector: complete XPath selector for element-specific screenshot.
                         If provided, full_page will be ignored.

    Returns:
        A success message with the full screenshot path.

    Example:
        take_screenshot("google.png")
        take_screenshot("homepage.png", full_page=False)
        take_screenshot("element.png", element_selector="html/body/div[1]/div[@id='main']")
        take_screenshot()  # Auto-generated filename
    """
    try:
        import os
        from datetime import datetime

        # 确保screenshots目录存在
        screenshots_dir = os.path.join(os.getcwd(), "screenshots")
        os.makedirs(screenshots_dir, exist_ok=True)

        # 如果没有提供文件名，生成时间戳文件名
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"screenshot_{timestamp}.png"

        # 确保文件名有.png扩展名
        if not filename.lower().endswith(".png"):
            filename += ".png"

        # 构建完整路径
        full_path = os.path.join(screenshots_dir, filename)

        # 验证参数组合
        if element_selector and full_page:
            logger.warning("full_page ignored when element_selector is provided")
            full_page = False

        browser = get_global_browser()
        result_path = run_browser_call(
            lambda: browser.screenshot(
                path=full_path, full_page=full_page, element_selector=element_selector
            )
        )
        success_msg = f"📸 Screenshot saved: {result_path}"
        logger.info(f"BrowserTool screenshot: {success_msg}")
        return success_msg
    except Exception as e:
        error_msg = f"Screenshot failed: {str(e)}"
        logger.error(f"BrowserTool screenshot error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def get_page_info() -> str:
    """
    Get information about the current page including title and URL.

    Returns:
        A string containing the page title and current URL.

    Example:
        get_page_info()
    """
    try:
        browser = get_global_browser()
        page_info = browser.get_page_info()
        title = page_info.get("title", "N/A")
        url = page_info.get("url", "N/A")
        result = f"📄 Page Title: {title}\n🔗 Current URL: {url}"
        logger.info(f"BrowserTool page_info: {result}")
        return result
    except Exception as e:
        error_msg = f"Failed to get page info: {str(e)}"
        logger.error(f"BrowserTool page_info error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def click_element(element_selector: str, description: str = "") -> str:
    """
    Click on an element using complete XPath selector.

    Args:
        element_selector: must be complete XPath selector for the element (must be obtained from DOM tools first)
        description: Human-readable description of the element

    Returns:
        A success message indicating click completion.

    Example:
        click_element("html/body/div[1]/form/button", "submit button")
    """
    try:
        run_browser_call(lambda: get_global_browser().click(element_selector))
        desc = f" ({description})" if description else ""
        success_msg = f"👆 Clicked element: {element_selector}{desc}"
        logger.info(f"BrowserTool click: {success_msg}")
        return success_msg
    except Exception as e:
        error_msg = f"Click failed: {str(e)}"
        logger.error(f"BrowserTool click error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def type_text(
    element_selector: str,
    text: str,
    description: str = "",
    clear_first: bool = True,
    submit: bool = False,
) -> str:
    """
    Type text into an element using complete XPath selector with advanced options.

    Args:
        element_selector: complete XPath selector for the element (must be obtained from DOM tools first)
        text: Text to type into the element
        description: Human-readable description of the element
        clear_first: Whether to clear existing text first
        submit: Whether to press Enter after typing

    Returns:
        A success message indicating typing completion.

    Example:
        type_text("html/body/div[1]/input[@name='search']", "john_doe", "username field")
        type_text("html/body/div[1]/input[@name='search']", "python tutorial", "search box", submit=True)
    """
    try:
        run_browser_call(
            lambda: get_global_browser().type_text(element_selector, text, clear_first)
        )

        # Handle submit if requested
        if submit:
            run_browser_call(lambda: get_global_browser().press_key("Enter"))
            # Wait for potential navigation after submit
            try:
                import time

                time.sleep(2)  # Give time for navigation to start
                browser = get_global_browser()
                if browser._page:
                    browser._page.wait_for_load_state("domcontentloaded", timeout=10000)
                    logger.info("Page ready after form submission")
            except Exception as e:
                logger.debug(f"Page wait after submit failed (continuing): {e}")

        desc = f" ({description})" if description else ""
        success_msg = f"⌨️ Typed '{text}' into: {element_selector}{desc}"
        if submit:
            success_msg += " and submitted"
        logger.info(f"BrowserTool type: {success_msg}")
        return success_msg
    except Exception as e:
        error_msg = f"Type text failed: {str(e)}"
        logger.error(f"BrowserTool type error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def scroll_page(
    direction: str = "down",
    amount: int = 1,
    unit: str = "pages",
) -> str:
    """
    Scroll the page using native Playwright methods with clean interface.

    Args:
        direction: Scroll direction - "down", "up", "left", "right", "top", "bottom"
        amount: Amount to scroll (default: 1)
        unit: Unit of measurement - "pages", "pixels" (default: "pages")

    Returns:
        A success message indicating scroll completion.

    Example:
        scroll_page()  # Scroll down 1 page
        scroll_page("up", 2)  # Scroll up 2 pages
        scroll_page("down", 500, "pixels")  # Scroll down 500 pixels
        scroll_page("top")  # Scroll to top
        scroll_page("bottom")  # Scroll to bottom
    """
    try:
        browser = get_global_browser()

        # Handle special directions first
        if direction == "top":
            run_browser_call(lambda: browser.scroll_to_top())
            success_msg = "⬆️ Scrolled to top of page"

        elif direction == "bottom":
            run_browser_call(lambda: browser.scroll_to_bottom())
            success_msg = "⬇️ Scrolled to bottom of page"

        else:
            # Validate direction
            if direction not in ["down", "up", "left", "right"]:
                return "❌ Direction must be one of: down, up, left, right, top, bottom"

            # Calculate scroll distance
            if unit == "pixels":
                x, y = 0, 0
                if direction == "down":
                    y = amount
                elif direction == "up":
                    y = -amount
                elif direction == "right":
                    x = amount
                elif direction == "left":
                    x = -amount

                action = f"{amount} pixels {direction}"

            elif unit == "pages":
                # Use viewport size for page-based scrolling
                viewport_info = run_browser_call(lambda: browser.get_page_info())
                viewport = viewport_info.get("viewport", {})
                viewport_height = viewport.get("height", 800)  # fallback
                viewport_width = viewport.get("width", 1200)   # fallback

                x, y = 0, 0
                if direction == "down":
                    y = viewport_height * amount
                elif direction == "up":
                    y = -viewport_height * amount
                elif direction == "right":
                    x = viewport_width * amount
                elif direction == "left":
                    x = -viewport_width * amount

                action = f"{amount} page(s) {direction}"
            else:
                return "❌ Unit must be 'pages' or 'pixels'"

            # Perform the scroll using native Playwright method
            run_browser_call(lambda: browser.scroll_by(x, y))

            direction_emoji = {"down": "⬇️", "up": "⬆️", "left": "⬅️", "right": "➡️"}[direction]
            success_msg = f"{direction_emoji} Scrolled {action}"

        logger.info(f"BrowserTool scroll_page: {success_msg}")
        return success_msg

    except Exception as e:
        error_msg = f"Scroll failed: {str(e)}"
        logger.error(f"BrowserTool scroll_page error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def scroll_to_element(element_selector: str, description: str = "") -> str:
    """
    Scroll to make an element visible using native Playwright method.

    Args:
        element_selector: complete XPath selector for the element to scroll to
        description: Human-readable description of the element

    Returns:
        A success message indicating scroll completion.

    Example:
        scroll_to_element("html/body/div[@id='footer']", "page footer")
    """
    try:
        browser = get_global_browser()
        run_browser_call(lambda: browser.scroll_to_element(element_selector))

        desc = f" ({description})" if description else ""
        success_msg = f"🎯 Scrolled to element: {element_selector}{desc}"
        logger.info(f"BrowserTool scroll_to_element: {success_msg}")
        return success_msg

    except Exception as e:
        error_msg = f"Scroll to element failed: {str(e)}"
        logger.error(f"BrowserTool scroll_to_element error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def select_option(
    element_selector: str,
    values: List[str],
    description: str = "",
    by_label: bool = False,
) -> str:
    """
    Select option(s) in a dropdown element.

    Args:
        element_selector: complete XPath selector for the select element (must be obtained from DOM tools first)
        values: List of values or labels to select
        description: Human-readable description of the element
        by_label: Whether to select by visible label text instead of value attribute

    Returns:
        A success message indicating selection completion.

    Example:
        select_option("html/body/div[1]/select[@id='country']", ["US"], "country dropdown")
        select_option("html/body/div[1]/select[@name='options']", ["Option 1", "Option 2"], "multi-select", by_label=True)
    """
    try:
        if not values:
            return "❌ Values list cannot be empty"

        results = []
        for value in values:
            try:
                if by_label:
                    run_browser_call(
                        lambda: get_global_browser().select_option(
                            selector=element_selector, label=value
                        )
                    )
                    results.append(f"Selected label '{value}': Success")
                else:
                    run_browser_call(
                        lambda: get_global_browser().select_option(
                            selector=element_selector, value=value
                        )
                    )
                    results.append(f"Selected value '{value}': Success")
            except Exception as e:
                results.append(f"Failed to select '{value}': {str(e)}")

        desc = f" ({description})" if description else ""
        success_msg = (
            f"🔽 Selection results for {element_selector}{desc}:\n" + "\n".join(results)
        )
        logger.info(f"BrowserTool select_option: {success_msg}")
        return success_msg
    except Exception as e:
        error_msg = f"Select option failed: {str(e)}"
        logger.error(f"BrowserTool select_option error: {error_msg}")
        return f"❌ {error_msg}"


# =============================================================================
# ADVANCED BROWSER TOOLS - Feature-rich operations for general-purpose agents
# =============================================================================


# =============================================================================
# LEGACY COMPATIBILITY FUNCTIONS
# =============================================================================


def get_tools() -> List[BaseTool]:
    """
    Get all browser tools used by WebAgent.

    Returns:
        List of 12 core browser automation tools used by WebAgent.
    """
    return [
        navigate_browser,
        click_element,
        type_text,
        select_option,
        scroll_page,
        scroll_to_element,
        take_screenshot,
        get_page_info,
        switch_to_page,
        get_all_pages_info,
        close_page,
        close_browser,
    ]


def get_browser_specific_tools() -> List[BaseTool]:
    """
    Get browser-specific tools (legacy compatibility).

    Returns:
        Same as get_tools() - all core browser tools.
    """
    return get_tools()


@tool
def switch_to_page(page_index: int) -> str:
    """
    切换到指定的页面（标签页）。

    Args:
        page_index: 页面索引（0为第一个页面）

    Returns:
        切换结果信息

    Example:
        switch_to_page(0)  # 切换到第一个页面
        switch_to_page(1)  # 切换到第二个页面
    """
    try:
        browser = get_global_browser()
        result = run_browser_call(lambda: browser.switch_to_page(page_index))
        success_msg = f"🔄 已切换到页面 {page_index}: {result['title']} ({result['total_pages']} 个页面)"
        logger.info(f"BrowserTool switch_to_page: {success_msg}")
        return success_msg
    except Exception as e:
        error_msg = f"切换页面失败: {str(e)}"
        logger.error(f"BrowserTool switch_to_page error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def get_all_pages_info() -> str:
    """
    获取所有打开页面的信息。

    Returns:
        所有页面的信息列表

    Example:
        get_all_pages_info()  # 显示所有页面信息
    """
    try:
        browser = get_global_browser()
        pages_info = run_browser_call(lambda: browser.get_pages_info())

        if not pages_info:
            return "❌ 没有打开的页面"

        result_lines = [f"📋 共有 {len(pages_info)} 个页面:"]
        for page_info in pages_info:
            status = "🔸 当前" if page_info['is_current'] else "  "
            result_lines.append(f"{status} 页面 {page_info['index']}: {page_info['title']} - {page_info['url']}")

        result = "\n".join(result_lines)
        logger.info(f"BrowserTool get_all_pages_info: 找到 {len(pages_info)} 个页面")
        return result

    except Exception as e:
        error_msg = f"获取页面信息失败: {str(e)}"
        logger.error(f"BrowserTool get_all_pages_info error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def close_page(page_index: int) -> str:
    """
    关闭指定的页面。

    Args:
        page_index: 要关闭的页面索引（0为第一个页面）

    Returns:
        关闭结果信息

    Example:
        close_page(1)  # 关闭第二个页面
    """
    try:
        browser = get_global_browser()
        result = run_browser_call(lambda: browser.close_page(page_index))
        success_msg = f"🗑️ 已关闭页面 {page_index}: {result['closed_page_title']} (剩余 {result['remaining_pages']} 个页面)"
        logger.info(f"BrowserTool close_page: {success_msg}")
        return success_msg
    except Exception as e:
        error_msg = f"关闭页面失败: {str(e)}"
        logger.error(f"BrowserTool close_page error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def close_browser() -> str:
    """
    关闭当前浏览器实例，释放资源。
    
    这个工具用于在任务完成后主动关闭浏览器，释放系统资源。
    建议在完成所有网页操作任务后调用。
    
    Returns:
        操作结果信息
        
    Example:
        close_browser()  # 关闭浏览器
    """
    try:
        from ..core.browser import close_global_browser
        
        close_global_browser()
        success_msg = "🔒 浏览器已关闭，资源已释放"
        logger.info(f"BrowserTool close_browser: {success_msg}")
        return success_msg
    except Exception as e:
        error_msg = f"关闭浏览器失败: {str(e)}"
        logger.error(f"BrowserTool close_browser error: {error_msg}")
        return f"❌ {error_msg}"


def cleanup_tools() -> None:
    """
    Clean up resources used by tools (e.g., close browser).

    This should be called when the application is shutting down
    or when tools are no longer needed.
    """
    try:
        from ..core.browser import close_global_browser

        close_global_browser()
        logger.info("Browser tools cleanup completed")
    except Exception as e:
        logger.error(f"Error during browser tools cleanup: {e}")


# Export core functions and tools
__all__ = [
    "get_tools",
    "get_browser_specific_tools",
    "cleanup_tools",
    "close_browser",
    # Core tools used by WebAgent
    "navigate_browser",
    "take_screenshot",
    "get_page_info",
    "click_element",
    "type_text",
    "scroll_page",
    "scroll_to_element",
    "select_option",
    # Page management tools
    "switch_to_page",
    "get_all_pages_info",
    "close_page",
]
