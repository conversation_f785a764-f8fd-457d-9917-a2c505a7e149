from playwright.sync_api import sync_playwright
import time

def run_booking_search_with_xpath():
    with sync_playwright() as p:
        # 启动浏览器（建议调试时设为 headless=False）
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()

        try:
            # 1. 导航到 Booking.com 首页
            print("正在打开 Booking.com...")
            page.goto("https://www.booking.com/index.zh-cn.html", wait_until="domcontentloaded")

            # 截图：首页状态
            page.screenshot(path="booking_home.png")

            # 验证页面标题
            assert "Booking.com" in page.title(), "未能加载 Booking 首页"
            print("首页加载成功")

            # 2. 输入目的地“上海迪士尼”
            print("输入目的地：上海迪士尼")
            dest_input_xpath = "/html/body/div[3]/div[2]/div/form/div/div[1]/div/div/div[1]/div/div/input"
            input_locator = page.locator("xpath=" + dest_input_xpath)
            input_locator.click()
            input_locator.fill("")  # 清空
            input_locator.fill("上海迪士尼")
            time.sleep(1.5)  # 等待建议弹出

            # ✅ 使用键盘选择第一个建议（最稳定，避免点击不可交互问题）
            input_locator.press("ArrowDown")
            time.sleep(0.3)
            input_locator.press("Enter")
            print("已通过键盘选择‘上海迪士尼’")

            # 3. 打开日期选择器
            print("选择入住和退房日期")
            date_button_xpath = "/html/body/div[3]/div[2]/div/form/div/div[2]/div/button"
            page.locator("xpath=" + date_button_xpath).click()
            time.sleep(0.5)

            # 选择 8月19日（入住）
            checkin_xpath = "/html/body/div[3]/div[2]/div/form/div/div[2]/div/div/div/nav/div[2]/div/div[1]/div/div[1]/table/tbody/tr[4]/td[3]/span"
            page.locator("xpath=" + checkin_xpath).click()
            time.sleep(0.3)

            # 选择 8月20日（退房）
            checkout_xpath = "/html/body/div[3]/div[2]/div/form/div/div[2]/div/div/div/nav/div[2]/div/div[1]/div/div[1]/table/tbody/tr[4]/td[4]/span"
            page.locator("xpath=" + checkout_xpath).click()
            time.sleep(0.5)

            # 4. 打开人数设置弹窗
            print("设置房客人數：2成人，1名9岁儿童")
            occupancy_button_xpath = "/html/body/div[3]/div[2]/div/form/div/div[3]/div/button"
            page.locator("xpath=" + occupancy_button_xpath).click()
            time.sleep(0.5)

            # 点击儿童“+”按钮
            child_add_xpath = "/html/body/div[3]/div[2]/div/form/div/div[3]/div/div/div/div/div[2]/div[2]/button[2]"
            page.locator("xpath=" + child_add_xpath).click()
            time.sleep(0.3)

            # 打开儿童年龄下拉框
            age_select_xpath = "/html/body/div[3]/div[2]/div/form/div/div[3]/div/div/div/div/div[3]/div/div/select"
            page.locator("xpath=" + age_select_xpath).click()
            time.sleep(0.3)

            # 选择“9岁”
            age_option_xpath = "/html/body/div[3]/div[2]/div/form/div/div[3]/div/div/div/div/div[3]/div/div/select/option[text()='9岁']"
            page.locator("xpath=" + age_option_xpath).select_option("9")
            time.sleep(0.3)

            # 点击“完成”按钮
            done_button_xpath = "/html/body/div[3]/div[2]/div/form/div/div[3]/div/div/div/button"
            page.locator("xpath=" + done_button_xpath).click()
            time.sleep(0.5)

            # 5. 点击搜索按钮
            search_button_xpath = "/html/body/div[3]/div[2]/div/form/div/div[4]/button"
            with page.expect_navigation(timeout=30000):  # 搜索会跳转
                page.locator("xpath=" + search_button_xpath).click()
            print("已提交搜索，正在加载结果页...")

            # 等待结果页加载完成
            page.wait_for_load_state("networkidle")
            assert "searchresults" in page.url, "未成功跳转到搜索结果页"
            print("已进入搜索结果页:", page.url)

            # 截图：搜索结果页
            page.screenshot(path="booking_search_results.png")

            # 6. 提取前三个酒店名称
            print("正在提取前三个酒店名称...")
            hotel_name_xpath = "//div[@data-testid='property-card']//div[@data-testid='title']"
            page.locator(hotel_name_xpath).first.wait_for_element_state("visible", timeout=10000)

            hotel_locator = page.locator("xpath=" + hotel_name_xpath)
            extracted_hotels = []
            for i in range(min(3, hotel_locator.count())):
                name = hotel_locator.nth(i).text_content().strip()
                extracted_hotels.append(name)

            print("前三个酒店名称：")
            for idx, name in enumerate(extracted_hotels, 1):
                print(f"{idx}. {name}")

            # 返回结构化结果
            result = {
                "status": "SUCCESS",
                "extracted_data": extracted_hotels,
                "page_info": {
                    "title": page.title(),
                    "url": page.url
                },
                "screenshots": ["booking_home.png", "booking_search_results.png"]
            }

            return result

        except Exception as e:
            print(f"脚本执行出错: {e}")
            page.screenshot(path="error.png")
            return {"status": "FAILED", "error": str(e)}

        finally:
            browser.close()

# 运行脚本
if __name__ == "__main__":
    result = run_booking_search_with_xpath()
    print("\n最终结果：", result)