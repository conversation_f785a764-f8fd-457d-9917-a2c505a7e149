"""
Smart Planner Agent module for unified task planning and prompt optimization.

This module provides a specialized agent that combines the capabilities of 
task planning and prompt optimization. It analyzes user intent, generates
clarifying questions, and creates optimized execution plans.
"""

import logging
from typing import Optional, List
from .prompts import get_smart_planner_prompt
from langchain.agents import create_openai_functions_agent, AgentExecutor
from langchain_openai import ChatOpenAI
from langchain_core.tools import BaseTool

from ..core.config import config

logger = logging.getLogger(__name__)


class SmartPlannerAgent:
    """
    统一的智能规划Agent，整合了提示词优化和任务规划功能。

    特性：
    - 使用专业的智能规划工具集
    - 支持任务分析和执行计划生成
    - 集成搜索能力获取实时信息
    - 简洁高效的执行接口
    """

    def __init__(
        self,
        tools: Optional[List[BaseTool]] = None,
        verbose: Optional[bool] = None,
        llm_config: Optional[dict] = None,
    ):
        """
        Initialize the SmartPlannerAgent.

        Args:
            tools: List of LangChain tools to use. If None, uses default smart planner tools.
            verbose: Whether to enable verbose logging.
            llm_config: Custom LLM configuration.
        """
        self.tools = tools or self._get_smart_planner_tools()
        logger.info(f"SmartPlannerAgent initialized with {len(self.tools)} tools: {[t.name for t in self.tools]}")

        self.verbose = verbose if verbose is not None else config.VERBOSE
        self.llm_config = llm_config or config.get_llm_config()

        # Lazy initialization
        self._llm = None
        self._agent = None
        self._executor = None

    def _get_smart_planner_tools(self) -> List[BaseTool]:
        """
        Get smart planner tools for the agent.

        Returns:
            List of smart planner tools including analysis, planning, datetime, and search tools
        """
        from ..tools.smart_planner_tools import get_smart_planner_tools
        from ..tools.search_tools import get_search_tools

        # 获取核心规划工具
        planner_tools = get_smart_planner_tools()

        # 在 agent 层面添加搜索工具
        search_tools = get_search_tools()

        # 合并工具列表
        all_tools = planner_tools + search_tools

        logger.info(f"SmartPlannerAgent tools: {len(planner_tools)} planner + {len(search_tools)} search = {len(all_tools)} total")

        return all_tools

    def _create_llm(self) -> ChatOpenAI:
        """Create and configure the LLM instance."""
        if self._llm is None:
            try:
                self._llm = ChatOpenAI(**self.llm_config)
                logger.info(f"SmartPlannerAgent LLM created with model: {self.llm_config.get('model')}")
            except Exception as e:
                logger.error(f"Failed to create LLM: {e}")
                raise
        return self._llm

    def _create_agent(self) -> None:
        """Create the LangChain agent."""
        if self._agent is None:
            try:
                llm = self._create_llm()
                prompt = get_smart_planner_prompt()
                self._agent = create_openai_functions_agent(llm, self.tools, prompt)
                logger.info("SmartPlannerAgent created successfully")
            except Exception as e:
                logger.error(f"Failed to create SmartPlannerAgent: {e}")
                raise

    def _create_executor(self) -> AgentExecutor:
        """Create the agent executor."""
        if self._executor is None:
            self._create_agent()
            try:
                self._executor = AgentExecutor(
                    agent=self._agent,
                    tools=self.tools,
                    verbose=self.verbose,
                    max_iterations=15,  # 稍微增加，因为整合了更多功能
                    handle_parsing_errors=True,
                    return_intermediate_steps=True,  # 返回中间步骤便于调试
                )
                logger.info("SmartPlannerAgent executor created successfully")
            except Exception as e:
                logger.error(f"Failed to create SmartPlannerAgent executor: {e}")
                raise
        return self._executor

    def invoke(self, input_text: str, context: Optional[str] = None) -> dict:
        """
        调用Agent执行智能规划任务。

        Args:
            input_text: 用户输入的任务描述
            context: 额外的上下文信息

        Returns:
            包含分析和计划结果的字典
        """
        executor = self._create_executor()

        try:
            logger.info(f"SmartPlannerAgent processing: {input_text[:100]}...")

            # 构建输入上下文
            full_input = input_text
            if context:
                full_input += f"\n\n额外上下文：{context}"

            result = executor.invoke({"input": full_input})

            logger.info("SmartPlannerAgent processing completed successfully")

            # 增强结果信息
            enhanced_result = result.copy()
            enhanced_result.update({
                "agent_type": "SmartPlannerAgent",
                "tools_used": [step[0].tool for step in result.get("intermediate_steps", []) if len(step) > 0],
            })

            return enhanced_result

        except Exception as e:
            logger.error(f"SmartPlannerAgent processing failed: {e}")
            return {
                "error": str(e),
                "agent_type": "SmartPlannerAgent",
                "input": input_text,
                "context": context
            }




def build_smart_planner_agent(
    tools: Optional[List[BaseTool]] = None,
    verbose: Optional[bool] = None,
    llm_config: Optional[dict] = None,
) -> SmartPlannerAgent:
    """
    Build and return a configured SmartPlannerAgent instance.

    Args:
        tools: List of LangChain tools to use
        verbose: Whether to enable verbose logging
        llm_config: Custom LLM configuration

    Returns:
        A configured SmartPlannerAgent instance
        
    Example:
        >>> agent = build_smart_planner_agent(verbose=True)
        >>> result = agent.invoke("爬取京东手机商品数据")
        >>> print(result["output"])
    """
    try:
        agent = SmartPlannerAgent(tools=tools, verbose=verbose, llm_config=llm_config)
        logger.info("SmartPlannerAgent built successfully")
        return agent
    except Exception as e:
        logger.error(f"Failed to build SmartPlannerAgent: {e}")
        raise


if __name__ == "__main__":
    # 测试SmartPlannerAgent
    print("Testing SmartPlannerAgent...")

    # 完整测试
    test_input = "爬取淘宝商品数据"
    agent = build_smart_planner_agent(verbose=True)
    result = agent.invoke(test_input)
    print(f"Result: {result.get('output', 'No output')}")
    print(f"Agent type: {result.get('agent_type', 'Unknown')}")
    print(f"Tools used: {result.get('tools_used', [])}")