"""
Web Search and Fetch Tools for iICrawlerMCP.

This module provides web search and content fetching capabilities that can be shared by all agents.
Implements:
- Google search via SerpAPI
- Webpage content fetching via Jina Reader API (converts to Markdown)

Usage Examples:
    # Search for information
    from iicrawlermcp.tools.search_tools import web_search_serp
    results = web_search_serp.invoke({"query": "Python tutorials", "num_results": 5})

    # Fetch webpage content as Markdown
    from iicrawlermcp.tools.search_tools import web_fetch_jina
    content = web_fetch_jina.invoke({"url": "https://www.example.com"})

    # Get all search tools for agents
    from iicrawlermcp.tools.search_tools import get_search_tools
    tools = get_search_tools()  # Returns [web_search_serp, web_fetch_jina]

Configuration:
    API keys are configured in src/iicrawlermcp/core/config.py:
    - SERPAPI_API_KEY: For Google search functionality
    - JINA_API_KEY: For webpage content fetching
"""

import logging
import requests
import sys
from pathlib import Path

from langchain_core.tools import tool
from serpapi import Client

# Handle relative imports when running directly
try:
    from ..core.config import config
except ImportError:
    # When running directly, add parent directories to path
    current_dir = Path(__file__).parent
    project_root = current_dir.parent.parent.parent
    sys.path.insert(0, str(project_root / "src"))
    from iicrawlermcp.core.config import config

logger = logging.getLogger(__name__)


@tool
def web_search_serp(query: str, num_results: int = 5) -> str:
    """
    Search the web using Google via SerpAPI.
    
    This tool searches Google and returns organic search results.
    Useful for:
    - Finding domain names for websites
    - Looking up technical terms and documentation
    - Researching best practices
    - Getting current information about topics
    
    Args:
        query: The search query string
        num_results: Maximum number of results to return (default: 5)
        
    Returns:
        Formatted string containing search results with titles, snippets, and links
        
    Example:
        web_search_serp("淘宝官网域名")
        web_search_serp("Python web scraping best practices", num_results=3)
    """
    try:
        # Get API key from config
        search_config = config.get_search_tools_config()
        api_key = search_config.get("serpapi_api_key")
        if not api_key:
            error_msg = "SERPAPI_API_KEY not found in configuration"
            logger.error(error_msg)
            return f"❌ {error_msg}"
        
        # Set up search parameters
        params = {
            "engine": "google",
            "q": query,
            "num": num_results  # Limit number of results
        }
        
        logger.info(f"Searching Google for: {query}")
        
        # Perform search using new Client API
        client = Client(api_key=api_key)
        results = client.search(params)
        
        # Check for errors
        if "error" in results:
            error_msg = f"Search API error: {results['error']}"
            logger.error(error_msg)
            return f"❌ {error_msg}"
        
        # Extract organic results
        organic_results = results.get("organic_results", [])
        
        if not organic_results:
            return f"No results found for query: {query}"
        
        # Format results
        formatted_results = []
        formatted_results.append(f"🔍 Search results for: '{query}'")
        formatted_results.append(f"Found {len(organic_results)} results:\n")
        
        for i, result in enumerate(organic_results[:num_results], 1):
            title = result.get("title", "No title")
            snippet = result.get("snippet", "No description available")
            link = result.get("link", "")
            
            formatted_results.append(f"{i}. **{title}**")
            formatted_results.append(f"   {snippet}")
            formatted_results.append(f"   🔗 {link}")
            formatted_results.append("")  # Empty line for spacing
        
        # Add answer box if available
        if "answer_box" in results:
            answer_box = results["answer_box"]
            if "answer" in answer_box:
                formatted_results.insert(2, f"📌 Quick Answer: {answer_box['answer']}\n")
            elif "snippet" in answer_box:
                formatted_results.insert(2, f"📌 Featured Snippet: {answer_box['snippet']}\n")
        
        # Add knowledge graph if available
        if "knowledge_graph" in results:
            kg = results["knowledge_graph"]
            if "description" in kg:
                formatted_results.insert(2, f"📚 Knowledge Graph: {kg.get('title', '')} - {kg['description']}\n")
        
        result_text = "\n".join(formatted_results)
        logger.info(f"Search completed successfully, found {len(organic_results)} results")
        
        return result_text
        
    except ImportError:
        error_msg = "serpapi package not installed. Please install it with: pip install google-search-results"
        logger.error(error_msg)
        return f"❌ {error_msg}"
    except Exception as e:
        error_msg = f"Search failed: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"


@tool
def web_fetch_jina(url: str) -> str:
    """
    Fetch webpage content and convert it to Markdown using Jina Reader API.

    This tool fetches the content of a specific webpage and converts it to
    clean, readable Markdown format. It's useful for:
    - Reading specific webpage content
    - Analyzing webpage structure and information
    - Extracting text content from articles or documentation
    - Getting clean, formatted content without HTML noise

    Args:
        url: The URL of the webpage to fetch (e.g., "https://www.booking.com")

    Returns:
        Markdown-formatted content of the webpage

    Example:
        web_fetch_jina("https://www.booking.com")
        web_fetch_jina("https://docs.python.org/3/tutorial/")
    """
    try:
        # Get API key from config
        search_config = config.get_search_tools_config()
        api_key = search_config.get("jina_api_key")
        if not api_key:
            error_msg = "JINA_API_KEY not found in configuration"
            logger.error(error_msg)
            return f"❌ {error_msg}"

        # Construct Jina Reader URL
        jina_url = f"https://r.jina.ai/{url}"

        # Set up headers
        headers = {
            "Authorization": f"Bearer {api_key}",
            "User-Agent": "iICrawlerMCP/1.0"
        }

        logger.info(f"Fetching webpage content from: {url}")

        # Make request to Jina Reader API
        response = requests.get(jina_url, headers=headers, timeout=30)

        # Check for HTTP errors
        if response.status_code != 200:
            error_msg = f"HTTP {response.status_code}: {response.reason}"
            logger.error(f"Jina API error: {error_msg}")
            return f"❌ Failed to fetch webpage: {error_msg}"

        # Get the content
        content = response.text.strip()

        if not content:
            return f"❌ No content found for URL: {url}"

        # Add metadata header
        result = f"📄 Webpage content from: {url}\n"
        result += f"📊 Content length: {len(content)} characters\n"
        result += "=" * 50 + "\n\n"
        result += content

        logger.info(f"Successfully fetched content from {url}, length: {len(content)} chars")

        return result

    except requests.exceptions.Timeout:
        error_msg = f"Request timeout while fetching: {url}"
        logger.error(error_msg)
        return f"❌ {error_msg}"
    except requests.exceptions.RequestException as e:
        error_msg = f"Request failed: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"
    except Exception as e:
        error_msg = f"Web fetch failed: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"


# Tool list for easy import
SEARCH_TOOLS = [
    web_search_serp,
    web_fetch_jina,
]


def get_search_tools():
    """
    Get all search tools.
    
    Returns:
        List of search tools that can be used by agents
    """
    return SEARCH_TOOLS.copy()

if __name__ == '__main__':
    # Test search
    print("=== Testing web search ===")
    print(web_search_serp("booking的官网是什么"))

    print("\n=== Testing web fetch ===")
    # Test fetch
    print(web_fetch_jina("https://www.booking.com"))